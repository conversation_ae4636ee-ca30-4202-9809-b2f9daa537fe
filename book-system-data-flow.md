
```mermaid
graph TD
    subgraph User Interaction (Renderer Process)
        A[User clicks "Add Book" in UI] --> B{Book Details Form};
        B --> C[User fills form and clicks "Save"];
        C --> D[ipcRenderer.invoke("add-book", bookDetails)];
    end

    subgraph Main Process (Electron Backend)
        D --> E{books-api.ts: handleAddBook};
        E --> F[database-api.ts: addBook];
        F --> G((Database));
        G -- New Book ID --> F;
        F -- New Book Object --> E;
        E --> H{folders-api.ts: createBookFolder};
        H --> I[/books/{book.id}/];
        I -- Folder Path --> E;
        E --> J[Generate and Save Cover];
        J --> K[/books/{book.id}/cover.jpg];
        K -- Cover Path --> E;
        E --> L[database-api.ts: updateBookCoverPath];
        L --> G;
        E -- Success Response --> D;
    end

    subgraph Note Creation Flow
        N[User clicks "New Note" for a book] --> O[ipcRenderer.invoke("add-note", { bookId })];
        O --> P{notes-api.ts: handleAddNote};
        P --> Q[database-api.ts: addNote];
        Q --> G;
        G -- New Note Object --> Q;
        Q -- New Note Object --> P;
        P -- Success Response --> O;
    end

    D -- Triggers --> E;
    O -- Triggers --> P;

    style G fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#ccf,stroke:#333,stroke-width:2px
    style K fill:#ccf,stroke:#333,stroke-width:2px
```

### Flow Description:

1.  **Add Book Request**: The process starts when the user fills out the book details form in the UI (Renderer Process) and clicks save. This action triggers an IPC call (`add-book`) to the main process with the book's data.

2.  **Backend Handling (`books-api.ts`)**:
    *   The `handleAddBook` function in `books-api.ts` receives the request.
    *   It first calls the `addBook` function in `database-api.ts`.

3.  **Database Interaction (`database-api.ts`)**:
    *   The `addBook` function inserts a new record into the `books` table in the database.
    *   It returns the newly created book object, including its unique ID, back to `books-api.ts`.

4.  **Folder Creation (`folders-api.ts`)**:
    *   Using the new book ID, `books-api.ts` calls `createBookFolder` from `folders-api.ts`.
    *   This creates a dedicated directory for the book on the file system (e.g., `.../books/{book.id}/`).

5.  **Book Cover Generation**:
    *   `books-api.ts` proceeds to generate a cover image for the book.
    *   This cover is saved into the newly created book folder.
    *   The path to the cover is then updated in the book's database record via another call to `database-api.ts`.

6.  **Note Creation**:
    *   When a user wants to add a note to a book, the UI sends the `bookId` via an `add-note` IPC call.
    *   The `handleAddNote` function in `notes-api.ts` calls `addNote` in `database-api.ts`.
    *   A new note record is created in the `notes` table, linked to the book via `book_id`. The new note object is returned to the UI.
