// Timer API - Functions for timer sessions and settings
import { getDatabase } from '../database/database';
import sqlite3 from 'sqlite3';

// Types
type Database = sqlite3.Database;

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface TimerSession {
    id: number;
    start_time: string;
    end_time?: string | null;
    duration?: number | null;
    session_type: string;
    is_completed: 0 | 1;
    created_at: string;
    updated_at?: string;
    focus?: string | null;
    category?: string | null;
    session_name?: string | null;
    pomodoro_cycles_completed?: number;
    is_user_session?: 0 | 1;
    actual_pomodoro_count?: number; // Computed field from SQL queries
}

interface PomodoroCycle {
    id: number;
    session_id: number;
    cycle_type: 'pomodoro' | 'short_break' | 'long_break';
    start_time: string;
    end_time?: string | null;
    duration?: number | null;
    completed: 0 | 1;
    created_at: string;
}

interface TimerStats {
    total_sessions: number;
    total_duration: number | null;
    work_sessions: number;
    work_duration: number | null;
    break_sessions: number;
    break_duration: number | null;
    total_pomodoros: number;
}

interface TimerSettings {
    id: number;
    work_duration: number;
    short_break_duration: number;
    long_break_duration: number;
    long_break_interval: number;
    auto_start_breaks: 0 | 1;
    auto_start_work: 0 | 1;
    created_at: string;
    updated_at: string;
}

interface DbRunResult {
    id: number;
    changes: number;
}

// ============================================================================
// DATABASE HELPERS
// ============================================================================

const dbGet = <T>(query: string, params: any[] = []): Promise<T | undefined> => {
    return new Promise((resolve, reject) => {
        try {
            const db: Database = getDatabase();
            db.get(query, params, (err: Error | null, row: T) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        } catch (error: any) {
            if (error.message.includes('Database is still initializing')) {
                // Retry after a short delay
                setTimeout(() => {
                    dbGet<T>(query, params).then(resolve).catch(reject);
                }, 100);
            } else {
                reject(error);
            }
        }
    });
};

const dbAll = <T>(query: string, params: any[] = []): Promise<T[]> => {
    return new Promise((resolve, reject) => {
        try {
            const db: Database = getDatabase();
            db.all(query, params, (err: Error | null, rows: T[]) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        } catch (error: any) {
            if (error.message.includes('Database is still initializing')) {
                // Retry after a short delay
                setTimeout(() => {
                    dbAll<T>(query, params).then(resolve).catch(reject);
                }, 100);
            } else {
                reject(error);
            }
        }
    });
};

const dbRun = (query: string, params: any[] = []): Promise<DbRunResult> => {
    return new Promise((resolve, reject) => {
        try {
            const db: Database = getDatabase();
            db.run(query, params, function (this: { lastID: number; changes: number }, err: Error | null) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        } catch (error: any) {
            if (error.message.includes('Database is still initializing')) {
                // Retry after a short delay
                setTimeout(() => {
                    dbRun(query, params).then(resolve).catch(reject);
                }, 100);
            } else {
                reject(error);
            }
        }
    });
};

// ============================================================================
// USER SESSION MANAGEMENT
// ============================================================================

export const createUserSession = async (
    sessionName: string, 
    focus?: string, 
    category?: string
): Promise<TimerSession> => {
    if (!sessionName || sessionName.trim() === '') {
        throw new Error('Session name is required.');
    }
    if (sessionName.length > 100) {
        throw new Error('Session name must be 100 characters or less.');
    }

    try {
        const startTime = new Date().toISOString();

        const result = await dbRun(
            `INSERT INTO timer_sessions (
                start_time, session_type, focus, category, session_name, 
                pomodoro_cycles_completed, is_user_session, is_completed, 
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)`,
            [startTime, 'work', focus || null, category || 'General', sessionName.trim(), 0, 1, startTime, startTime]
        );

        const newSession = await dbGet<TimerSession>('SELECT * FROM timer_sessions WHERE id = ?', [result.id]);
        if (!newSession) {
            throw new Error('Failed to retrieve the user session after creation.');
        }
        return newSession;
    } catch (error: any) {
        console.error('Error creating user session:', error);
        throw new Error(`Failed to create user session: ${error.message}`);
    }
};

export const endUserSession = async (sessionId: number, endDate?: Date): Promise<TimerSession> => {
    if (typeof sessionId !== 'number') {
        throw new Error('Session ID must be a number.');
    }

    try {
        const endTime = endDate ? endDate.toISOString() : 'CURRENT_TIMESTAMP';
        const result = await dbRun(`
            UPDATE timer_sessions
            SET
                end_time = ?,
                duration = CAST((julianday(?) - julianday(start_time)) * 86400 AS INTEGER),
                is_completed = 1,
                updated_at = ?
            WHERE id = ? AND is_completed = 0 AND is_user_session = 1
        `, [endTime, endTime, endTime, sessionId]);

        if (result.changes === 0) {
            const existingSession = await dbGet<TimerSession>(
                'SELECT id, is_completed, is_user_session FROM timer_sessions WHERE id = ?', 
                [sessionId]
            );
            
            if (!existingSession) {
                throw new Error(`User session with ID ${sessionId} not found.`);
            }
            if (existingSession.is_completed === 1) {
                console.warn(`User session ${sessionId} was already completed.`);
                return await getTimerSession(sessionId);
            }
            if (existingSession.is_user_session === 0) {
                throw new Error(`Session ${sessionId} is not a user session.`);
            }
            throw new Error(`Failed to end user session ${sessionId}. Unknown reason.`);
        }

        return await getTimerSession(sessionId);
    } catch (error: any) {
        console.error(`Error ending user session ${sessionId}:`, error);
        throw new Error(`Failed to end user session ${sessionId}: ${error.message}`);
    }
};

export const getActiveUserSession = async (): Promise<TimerSession | null> => {
    try {
        const session = await dbGet<TimerSession>(`
            SELECT
                ts.*,
                COALESCE(
                    (SELECT COUNT(*) FROM pomodoro_cycles
                     WHERE session_id = ts.id AND cycle_type = 'pomodoro' AND completed = 1),
                    0
                ) as actual_pomodoro_count
            FROM timer_sessions ts
            WHERE ts.is_user_session = 1 AND ts.is_completed = 0
            ORDER BY ts.start_time DESC
            LIMIT 1
        `);

        if (session && session.actual_pomodoro_count !== session.pomodoro_cycles_completed) {
            await dbRun(
                'UPDATE timer_sessions SET pomodoro_cycles_completed = ? WHERE id = ?',
                [session.actual_pomodoro_count, session.id]
            );
            session.pomodoro_cycles_completed = session.actual_pomodoro_count;
        }

        return session || null;
    } catch (error: any) {
        console.error('Error getting active user session:', error);
        throw new Error(`Failed to get active user session: ${error.message}`);
    }
};

export const updateSession = async (
    sessionId: number, 
    updateData: {
        duration?: number;
        pomodoro_cycles_completed?: number;
        focus?: string;
        category?: string;
        session_name?: string;
    }
): Promise<TimerSession> => {
    if (typeof sessionId !== 'number') {
        throw new Error('Session ID must be a number.');
    }

    try {
        const existingSession = await dbGet<TimerSession>('SELECT id FROM timer_sessions WHERE id = ?', [sessionId]);
        if (!existingSession) {
            throw new Error(`Session with ID ${sessionId} not found.`);
        }

        const fieldsToUpdate: string[] = [];
        const params: any[] = [];
        const now = new Date().toISOString();

        Object.entries(updateData).forEach(([key, value]) => {
            if (value !== undefined) {
                if (key === 'duration' && (typeof value !== 'number' || value < 0)) {
                    throw new Error('Duration must be a non-negative number.');
                }
                if (key === 'pomodoro_cycles_completed' && (typeof value !== 'number' || value < 0)) {
                    throw new Error('Pomodoro cycles completed must be a non-negative number.');
                }

                fieldsToUpdate.push(`${key} = ?`);
                params.push(value);
            }
        });

        if (fieldsToUpdate.length === 0) {
            console.warn('No valid fields provided for session update.');
            return await getTimerSession(sessionId);
        }

        fieldsToUpdate.push('updated_at = ?');
        params.push(now, sessionId);

        const query = `UPDATE timer_sessions SET ${fieldsToUpdate.join(', ')} WHERE id = ?`;
        const result = await dbRun(query, params);

        if (result.changes === 0) {
            throw new Error(`Failed to update session ${sessionId}.`);
        }

        return await getTimerSession(sessionId);
    } catch (error: any) {
        console.error(`Error updating session ${sessionId}:`, error);
        throw new Error(`Failed to update session ${sessionId}: ${error.message}`);
    }
};

// ============================================================================
// POMODORO CYCLE MANAGEMENT
// ============================================================================

export const startPomodoroInSession = async (sessionId: number, cycleType: string): Promise<PomodoroCycle> => {
    if (typeof sessionId !== 'number') {
        throw new Error('Session ID must be a number.');
    }
    if (!['pomodoro', 'short_break', 'long_break'].includes(cycleType)) {
        throw new Error('Cycle type must be pomodoro, short_break, or long_break.');
    }

    try {
        const session = await dbGet<TimerSession>('SELECT id, is_completed FROM timer_sessions WHERE id = ?', [sessionId]);
        if (!session) {
            throw new Error(`Session with ID ${sessionId} not found.`);
        }
        if (session.is_completed === 1) {
            throw new Error(`Session ${sessionId} is already completed.`);
        }

        const startTime = new Date().toISOString();

        const result = await dbRun(
            'INSERT INTO pomodoro_cycles (session_id, cycle_type, start_time, completed, created_at) VALUES (?, ?, ?, 0, ?)',
            [sessionId, cycleType, startTime, startTime]
        );

        const newCycle = await dbGet<PomodoroCycle>('SELECT * FROM pomodoro_cycles WHERE id = ?', [result.id]);
        if (!newCycle) {
            throw new Error('Failed to retrieve the pomodoro cycle after creation.');
        }
        return newCycle;
    } catch (error: any) {
        console.error('Error starting pomodoro cycle:', error);
        throw new Error(`Failed to start pomodoro cycle: ${error.message}`);
    }
};

export const completePomodoroInSession = async (sessionId: number, cycleId: number): Promise<PomodoroCycle> => {
    if (typeof sessionId !== 'number' || typeof cycleId !== 'number') {
        throw new Error('Session ID and Cycle ID must be numbers.');
    }

    const db = getDatabase();
    
    // Use a transaction to ensure atomic updates across both tables
    return new Promise<PomodoroCycle>((resolve, reject) => {
        db.serialize(() => {
            db.run('BEGIN IMMEDIATE TRANSACTION');
            
            db.run(`
                UPDATE pomodoro_cycles
                SET
                    end_time = CURRENT_TIMESTAMP,
                    duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 86400 AS INTEGER),
                    completed = 1
                WHERE id = ? AND session_id = ? AND completed = 0
            `, [cycleId, sessionId], function(err) {
                if (err) {
                    db.run('ROLLBACK');
                    reject(new Error(`Failed to complete pomodoro cycle: ${err.message}`));
                    return;
                }
                
                if (this.changes === 0) {
                    db.run('ROLLBACK');
                    reject(new Error(`Failed to complete pomodoro cycle ${cycleId} in session ${sessionId}.`));
                    return;
                }
                
                // Check if this is a pomodoro cycle (not break) to increment session counter
                db.get('SELECT cycle_type FROM pomodoro_cycles WHERE id = ?', [cycleId], (err, cycle: any) => {
                    if (err) {
                        db.run('ROLLBACK');
                        reject(new Error(`Failed to retrieve cycle type: ${err.message}`));
                        return;
                    }
                    
                    if (cycle?.cycle_type === 'pomodoro') {
                        db.run(
                            'UPDATE timer_sessions SET pomodoro_cycles_completed = pomodoro_cycles_completed + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                            [sessionId],
                            function(sessionErr) {
                                if (sessionErr) {
                                    db.run('ROLLBACK');
                                    reject(new Error(`Failed to update session: ${sessionErr.message}`));
                                    return;
                                }
                                
                                // Commit transaction and return completed cycle
                                db.run('COMMIT', (commitErr) => {
                                    if (commitErr) {
                                        reject(new Error(`Failed to commit transaction: ${commitErr.message}`));
                                        return;
                                    }
                                    
                                    // Retrieve the completed cycle
                                    db.get('SELECT * FROM pomodoro_cycles WHERE id = ?', [cycleId], (getCycleErr, completedCycle: any) => {
                                        if (getCycleErr || !completedCycle) {
                                            reject(new Error('Failed to retrieve completed pomodoro cycle.'));
                                            return;
                                        }
                                        resolve(completedCycle);
                                    });
                                });
                            }
                        );
                    } else {
                        // Not a pomodoro cycle, just commit and return
                        db.run('COMMIT', (commitErr) => {
                            if (commitErr) {
                                reject(new Error(`Failed to commit transaction: ${commitErr.message}`));
                                return;
                            }
                            
                            // Retrieve the completed cycle
                            db.get('SELECT * FROM pomodoro_cycles WHERE id = ?', [cycleId], (getCycleErr, completedCycle: any) => {
                                if (getCycleErr || !completedCycle) {
                                    reject(new Error('Failed to retrieve completed pomodoro cycle.'));
                                    return;
                                }
                                resolve(completedCycle);
                            });
                        });
                    }
                });
            });
        });
    });
};

export const getActiveCycleForSession = async (sessionId: number): Promise<PomodoroCycle | null> => {
    if (typeof sessionId !== 'number') {
        throw new Error('Session ID must be a number.');
    }

    try {
        const cycle = await dbGet<PomodoroCycle>(
            'SELECT * FROM pomodoro_cycles WHERE session_id = ? AND completed = 0 ORDER BY start_time DESC LIMIT 1',
            [sessionId]
        );
        return cycle || null;
    } catch (error: any) {
        console.error('Error getting active cycle for session:', error);
        throw new Error(`Failed to get active cycle for session: ${error.message}`);
    }
};

export const cancelActiveCycle = async (sessionId: number, cycleId: number): Promise<void> => {
    if (typeof sessionId !== 'number' || typeof cycleId !== 'number') {
        throw new Error('Session ID and Cycle ID must be numbers.');
    }

    try {
        const result = await dbRun(
            'DELETE FROM pomodoro_cycles WHERE id = ? AND session_id = ? AND completed = 0',
            [cycleId, sessionId]
        );

        if (result.changes === 0) {
            console.warn(`No active cycle found to cancel: cycleId=${cycleId}, sessionId=${sessionId}`);
        }
    } catch (error: any) {
        console.error('Error canceling active cycle:', error);
        throw new Error(`Failed to cancel active cycle: ${error.message}`);
    }
};

// ============================================================================
// TIMER SESSIONS (LEGACY SUPPORT)
// ============================================================================

export const startTimerSession = async (
    sessionType: string = 'work',
    focus?: string,
    category?: string
): Promise<TimerSession> => {
    if (typeof sessionType !== 'string' || sessionType.trim() === '') {
        throw new Error('Session type must be a non-empty string.');
    }
    
    try {
        const startTime = new Date().toISOString();

        const result = await dbRun(
            `INSERT INTO timer_sessions (
                start_time, session_type, focus, category, session_name, 
                pomodoro_cycles_completed, is_user_session, is_completed, 
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)`,
            [startTime, sessionType, focus || null, category || null, null, 0, 0, startTime, startTime]
        );

        const newSession = await dbGet<TimerSession>('SELECT * FROM timer_sessions WHERE id = ?', [result.id]);
        if (!newSession) {
            throw new Error('Failed to retrieve the timer session after creation.');
        }
        return newSession;
    } catch (error: any) {
        console.error('Error starting timer session:', error);
        throw new Error(`Failed to start timer session: ${error.message}`);
    }
};

export const endTimerSession = async (sessionId: number): Promise<TimerSession> => {
    if (typeof sessionId !== 'number') {
        throw new Error('Session ID must be a number.');
    }
    
    try {
        const result = await dbRun(`
            UPDATE timer_sessions
            SET
                end_time = CURRENT_TIMESTAMP,
                duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 86400 AS INTEGER),
                is_completed = 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND is_completed = 0
        `, [sessionId]);

        if (result.changes === 0) {
            const existingSession = await dbGet<TimerSession>(
                'SELECT id, is_completed FROM timer_sessions WHERE id = ?', 
                [sessionId]
            );
            
            if (!existingSession) {
                throw new Error(`Timer session with ID ${sessionId} not found.`);
            }
            if (existingSession.is_completed === 1) {
                console.warn(`Timer session ${sessionId} was already completed.`);
                return await getTimerSession(sessionId);
            }
            throw new Error(`Failed to end timer session ${sessionId}. Unknown reason.`);
        }

        return await getTimerSession(sessionId);
    } catch (error: any) {
        console.error(`Error ending timer session ${sessionId}:`, error);
        throw new Error(`Failed to end timer session ${sessionId}: ${error.message}`);
    }
};

export const getTimerSession = async (sessionId: number): Promise<TimerSession> => {
    if (typeof sessionId !== 'number') {
        throw new Error('Session ID must be a number.');
    }
    
    try {
        const session = await dbGet<TimerSession>('SELECT * FROM timer_sessions WHERE id = ?', [sessionId]);
        if (!session) {
            throw new Error(`Timer session with ID ${sessionId} not found.`);
        }
        return session;
    } catch (error: any) {
        console.error(`Error getting timer session ${sessionId}:`, error);
        if (error.message.includes('not found')) throw error;
        throw new Error(`Failed to get timer session ${sessionId}: ${error.message}`);
    }
};

export const deleteTimerSession = async (sessionId: number): Promise<{ success: boolean; id: number }> => {
    if (typeof sessionId !== 'number') {
        throw new Error('Session ID must be a number.');
    }
    
    try {
        const result = await dbRun('DELETE FROM timer_sessions WHERE id = ?', [sessionId]);
        if (result.changes === 0) {
             console.warn(`Attempted to delete non-existent timer session with ID ${sessionId}`);
        }
        return { success: result.changes > 0, id: sessionId };
    } catch (error: any) {
        console.error(`Error deleting timer session ${sessionId}:`, error);
        throw new Error(`Failed to delete timer session ${sessionId}: ${error.message}`);
    }
};

// ============================================================================
// SESSION QUERIES & STATISTICS
// ============================================================================

export const getTimerSessionsByDateRange = async (startDate: string, endDate: string): Promise<TimerSession[]> => {
    if (!/\d{4}-\d{2}-\d{2}/.test(startDate) || !/\d{4}-\d{2}-\d{2}/.test(endDate)) {
        throw new Error('Start and end dates must be in YYYY-MM-DD format.');
    }
    
    try {
        const sessions = await dbAll<TimerSession>(`
            SELECT
                ts.*,
                COALESCE(
                    (SELECT COUNT(*) FROM pomodoro_cycles
                     WHERE session_id = ts.id AND cycle_type = 'pomodoro' AND completed = 1),
                    0
                ) as actual_pomodoro_count
            FROM timer_sessions ts
            WHERE date(ts.start_time) BETWEEN date(?) AND date(?)
            ORDER BY ts.start_time DESC
        `, [startDate, endDate]);

        for (const session of sessions) {
            if (session.actual_pomodoro_count !== session.pomodoro_cycles_completed) {
                await dbRun(
                    'UPDATE timer_sessions SET pomodoro_cycles_completed = ? WHERE id = ?',
                    [session.actual_pomodoro_count, session.id]
                );
                session.pomodoro_cycles_completed = session.actual_pomodoro_count;
            }
        }

        return sessions;
    } catch (error: any) {
        console.error('Error getting timer sessions by date range:', error);
        throw new Error(`Failed to get timer sessions by date range: ${error.message}`);
    }
};

export const getTodayTimerSessions = async (): Promise<TimerSession[]> => {
    try {
        return await dbAll<TimerSession>(
            "SELECT * FROM timer_sessions WHERE date(start_time) = date('now', 'localtime') ORDER BY start_time DESC"
        );
    } catch (error: any) {
        console.error('Error getting today timer sessions:', error);
        throw new Error(`Failed to get today's timer sessions: ${error.message}`);
    }
};

export const getTimerStatsByDateRange = async (startDate: string, endDate: string): Promise<TimerStats> => {
    if (!/\d{4}-\d{2}-\d{2}/.test(startDate) || !/\d{4}-\d{2}-\d{2}/.test(endDate)) {
        throw new Error('Start and end dates must be in YYYY-MM-DD format.');
    }
    
    try {
        const stats = await dbGet<TimerStats>(`
            SELECT
                COUNT(*) as total_sessions,
                COALESCE(SUM(duration), 0) as total_duration,
                COUNT(CASE WHEN session_type = 'work' THEN 1 END) as work_sessions,
                COALESCE(SUM(CASE WHEN session_type = 'work' THEN duration ELSE 0 END), 0) as work_duration,
                COUNT(CASE WHEN session_type = 'break' THEN 1 END) as break_sessions,
                COALESCE(SUM(CASE WHEN session_type = 'break' THEN duration ELSE 0 END), 0) as break_duration,
                COALESCE(
                    (SELECT COUNT(*) FROM pomodoro_cycles pc
                     WHERE pc.session_id IN (
                         SELECT id FROM timer_sessions
                         WHERE date(start_time) BETWEEN date(?) AND date(?) AND is_completed = 1
                     ) AND pc.cycle_type = 'pomodoro' AND pc.completed = 1),
                    0
                ) as total_pomodoros
            FROM timer_sessions
            WHERE date(start_time) BETWEEN date(?) AND date(?)
            AND is_completed = 1
        `, [startDate, endDate, startDate, endDate]);

        if (!stats) {
            return { 
                total_sessions: 0, 
                total_duration: 0, 
                work_sessions: 0, 
                work_duration: 0, 
                break_sessions: 0, 
                break_duration: 0, 
                total_pomodoros: 0 
            };
        }
        return stats;
    } catch (error: any) {
        console.error('Error getting timer stats by date range:', error);
        throw new Error(`Failed to get timer stats by date range: ${error.message}`);
    }
};

// ============================================================================
// TIMER SETTINGS
// ============================================================================

export const getTimerSettings = async (): Promise<TimerSettings> => {
    try {
        let settings = await dbGet<TimerSettings>('SELECT * FROM timer_settings ORDER BY id LIMIT 1');

        if (!settings) {
            console.log('No timer settings found, creating defaults.');
            
            const defaultSettings = {
                work_duration: 1500,
                short_break_duration: 300,
                long_break_duration: 900,
                long_break_interval: 4,
                auto_start_breaks: 1,
                auto_start_work: 1,
            };
            
            const now = new Date().toISOString();
            const result = await dbRun(`
                INSERT INTO timer_settings (
                    work_duration, short_break_duration, long_break_duration,
                    long_break_interval, auto_start_breaks, auto_start_work,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                defaultSettings.work_duration,
                defaultSettings.short_break_duration,
                defaultSettings.long_break_duration,
                defaultSettings.long_break_interval,
                defaultSettings.auto_start_breaks,
                defaultSettings.auto_start_work,
                now,
                now
            ]);

            settings = await dbGet<TimerSettings>('SELECT * FROM timer_settings WHERE id = ?', [result.id]);
            if (!settings) {
                throw new Error('Failed to retrieve default timer settings after creation.');
            }
        }

        return settings;
    } catch (error: any) {
        console.error('Error getting timer settings:', error);
        throw new Error(`Failed to get timer settings: ${error.message}`);
    }
};

export const updateTimerSettings = async (
    settingsUpdates: Partial<Omit<TimerSettings, 'id' | 'created_at' | 'updated_at'>>
): Promise<TimerSettings> => {
    try {
        const currentSettings = await getTimerSettings();

        const fieldsToUpdate: string[] = [];
        const params: any[] = [];
        const now = new Date().toISOString();

        (Object.keys(settingsUpdates) as Array<keyof typeof settingsUpdates>).forEach(key => {
            const value = settingsUpdates[key];
            if (value !== undefined) {
                if ((key.includes('_duration') || key.includes('_interval')) && (typeof value !== 'number' || value < 0)) {
                    throw new Error(`Invalid value for ${key}: must be a non-negative number.`);
                }
                if ((key.includes('_start_')) && (value !== 0 && value !== 1 && typeof value !== 'boolean')) {
                    throw new Error(`Invalid value for ${key}: must be boolean, 0, or 1.`);
                }

                fieldsToUpdate.push(`${key} = ?`);
                params.push(typeof value === 'boolean' ? (value ? 1 : 0) : value);
            }
        });

        if (fieldsToUpdate.length === 0) {
            console.warn('No valid fields provided for timer settings update.');
            return currentSettings;
        }

        fieldsToUpdate.push('updated_at = ?');
        params.push(now, currentSettings.id);

        const query = `UPDATE timer_settings SET ${fieldsToUpdate.join(', ')} WHERE id = ?`;
        await dbRun(query, params);

        return await getTimerSettings();
    } catch (error: any) {
        console.error('Error updating timer settings:', error);
        throw new Error(`Failed to update timer settings: ${error.message}`);
    }
};

export const resetTimerSettings = async (): Promise<TimerSettings> => {
    try {
        const currentSettings = await getTimerSettings();

        const defaultSettings = {
            work_duration: 1500,
            short_break_duration: 300,
            long_break_duration: 900,
            long_break_interval: 4,
            auto_start_breaks: 1,
            auto_start_work: 1,
        };
        
        const now = new Date().toISOString();

        await dbRun(`
            UPDATE timer_settings SET
                work_duration = ?, short_break_duration = ?, long_break_duration = ?,
                long_break_interval = ?, auto_start_breaks = ?, auto_start_work = ?,
                updated_at = ?
            WHERE id = ?
        `, [
            defaultSettings.work_duration,
            defaultSettings.short_break_duration,
            defaultSettings.long_break_duration,
            defaultSettings.long_break_interval,
            defaultSettings.auto_start_breaks,
            defaultSettings.auto_start_work,
            now,
            currentSettings.id
        ]);

        return await getTimerSettings();
    } catch (error: any) {
        console.error('Error resetting timer settings:', error);
        throw new Error(`Failed to reset timer settings: ${error.message}`);
    }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export const syncAllSessionPomodoroCounts = async (): Promise<{ updated: number; total: number }> => {
    try {
        const sessions = await dbAll<any>(`
            SELECT
                ts.id,
                ts.pomodoro_cycles_completed,
                COALESCE(
                    (SELECT COUNT(*) FROM pomodoro_cycles
                     WHERE session_id = ts.id AND cycle_type = 'pomodoro' AND completed = 1),
                    0
                ) as actual_pomodoro_count
            FROM timer_sessions ts
        `);

        let updatedCount = 0;

        for (const session of sessions) {
            if (session.actual_pomodoro_count !== session.pomodoro_cycles_completed) {
                await dbRun(
                    'UPDATE timer_sessions SET pomodoro_cycles_completed = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [session.actual_pomodoro_count, session.id]
                );
                updatedCount++;
            }
        }

        console.log(`Synced pomodoro counts: ${updatedCount} sessions updated out of ${sessions.length} total`);
        return { updated: updatedCount, total: sessions.length };
    } catch (error: any) {
        console.error('Error syncing session pomodoro counts:', error);
        throw new Error(`Failed to sync session pomodoro counts: ${error.message}`);
    }
};

// ============================================================================
// DEFAULT EXPORT
// ============================================================================

export default {
    // User session management
    createUserSession,
    endUserSession,
    getActiveUserSession,
    updateSession,

    // Pomodoro cycle management
    startPomodoroInSession,
    completePomodoroInSession,
    getActiveCycleForSession,
    cancelActiveCycle,

    // Timer sessions (legacy)
    startTimerSession,
    endTimerSession,
    getTimerSession,
    deleteTimerSession,

    // Session queries & statistics
    getTimerSessionsByDateRange,
    getTodayTimerSessions,
    getTimerStatsByDateRange,

    // Timer settings
    getTimerSettings,
    updateTimerSettings,
    resetTimerSettings,

    // Utility functions
    syncAllSessionPomodoroCounts
};