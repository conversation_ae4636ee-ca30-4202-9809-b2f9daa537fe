// stores/timerStore.ts - Fully Fixed Version
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useElectronAPI } from '../useElectronAPI'
import { useSettingsStore } from './settingsStore'
import type { TimerSession, PomodoroCycle } from '../types'

export interface TimerState {
    // Timer execution state
    isRunning: boolean
    timerType: 'pomodoro' | 'shortBreak' | 'longBreak'
    timeLeft: number // seconds remaining in current cycle
    wasTimerStartedThisCycle: boolean; // NEW: Track if timer was ever started

    // Session data
    activeSession: TimerSession | null
    currentCycleId: number | null
    currentSessionId: number | null

    // Progress tracking
    pomodoroCount: number
    totalFocusTime: number // total seconds of focus time

    // Timer settings (cached from database)
    pomodoroTime: number // 25 * 60 (25 minutes)
    shortBreakTime: number // 5 * 60 (5 minutes)
    longBreakTime: number // 15 * 60 (15 minutes)
    longBreakInterval: number // 4 (every 4 pomodoros)

    // Auto-start settings
    autoStartBreaks: boolean
    autoStartPomodoros: boolean
}

export const useTimerStore = defineStore('timer', () => {
    const db = useElectronAPI()

    console.log('🏪 [TimerStore] Initializing timer store...')

    // ===== STATE =====
    const state = ref<TimerState>({
        isRunning: false,
        timerType: 'pomodoro',
        timeLeft: 25 * 60, // Default 25 minutes
        wasTimerStartedThisCycle: false, // NEW: Add this line

        activeSession: null,
        currentCycleId: null,
        currentSessionId: null,

        pomodoroCount: 0,
        totalFocusTime: 0,

        // Default timer settings
        pomodoroTime: 25 * 60,
        shortBreakTime: 5 * 60,
        longBreakTime: 15 * 60,
        longBreakInterval: 4,

        autoStartBreaks: true,
        autoStartPomodoros: true
    })

    console.log('🏪 [TimerStore] Initial state:', JSON.stringify(state.value, null, 2))

    // Timer interval reference
    let timerInterval: number | null = null

    // ===== COMPUTED =====
    const formattedTime = computed(() => {
        const minutes = Math.floor(state.value.timeLeft / 60)
        const seconds = state.value.timeLeft % 60
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    })

    const formattedMinutes = computed(() => {
        const minutes = Math.floor(state.value.timeLeft / 60)
        return minutes.toString().padStart(2, '0')
    })

    const formattedSeconds = computed(() => {
        const seconds = state.value.timeLeft % 60
        return seconds.toString().padStart(2, '0')
    })

    const formattedFocusTime = computed(() => {
        const hours = Math.floor(state.value.totalFocusTime / 3600)
        const minutes = Math.floor((state.value.totalFocusTime % 3600) / 60)
        return `${hours}h ${minutes}m`
    })

    const sessionActive = computed(() => {
        const active = state.value.activeSession !== null
        console.log('🔄 [TimerStore] sessionActive computed:', active, 'activeSession:', state.value.activeSession?.id)
        return active
    })

    const canStartSession = computed(() => {
        const canStart = state.value.timerType === 'pomodoro'
        console.log('🔄 [TimerStore] canStartSession computed:', canStart, 'timerType:', state.value.timerType)
        return canStart
    })

    // ===== HELPER FUNCTIONS =====
    function getCurrentCycleDuration(): number {
        let duration: number
        switch (state.value.timerType) {
            case 'pomodoro':
                duration = state.value.pomodoroTime
                break
            case 'shortBreak':
                duration = state.value.shortBreakTime
                break
            case 'longBreak':
                duration = state.value.longBreakTime
                break
            default:
                duration = state.value.pomodoroTime
        }
        console.log('📏 [TimerStore] getCurrentCycleDuration:', duration, 'for type:', state.value.timerType)
        return duration
    }

    function getTimeOfDayPrefix(date: Date): string {
        const hour = date.getHours()
        let prefix: string
        if (hour < 12) prefix = 'Morning Work'
        else if (hour < 17) prefix = 'Afternoon Work'
        else prefix = 'Evening Work'

        console.log('🌅 [TimerStore] getTimeOfDayPrefix:', prefix, 'for hour:', hour)
        return prefix
    }

    // ===== DISCORD INTEGRATION =====
    function updateDiscordPresence() {
        try {
            const settingsStore = useSettingsStore()

            // Only update if Discord is enabled by user
            if (!settingsStore.settings.discordRichPresenceEnabled) {
                return
            }

            if (state.value.activeSession && (state.value.isRunning || state.value.timeLeft < getCurrentCycleDuration())) {
                // Timer is running or has been started, show detailed timer activity
                db.discord.setActivity({
                    type: 'timer',
                    timerType: state.value.timerType,
                    timeRemaining: state.value.timeLeft,
                    pomodoroCount: state.value.pomodoroCount,
                    sessionName: state.value.activeSession.name,
                    sessionCategory: state.value.activeSession.category,
                    totalFocusTime: state.value.totalFocusTime,
                    isRunning: state.value.isRunning
                }).catch((error: any) => {
                    console.error('🎮 [TimerStore] Failed to update Discord presence:', error)
                })
            }
            // Note: We don't set idle here - the Discord RPC will automatically
            // set idle after 3 minutes of inactivity. This allows the user to
            // be active in other parts of the app without immediately showing idle.
        } catch (error) {
            console.error('🎮 [TimerStore] Failed to update Discord presence:', error)
        }
    }

    // ===== CORE TIMER ACTIONS =====
    function switchTimerType(type: 'pomodoro' | 'shortBreak' | 'longBreak', shouldAutoStart: boolean = false) {
        console.log('🔄 [TimerStore] switchTimerType called:', {
            from: state.value.timerType,
            to: type,
            shouldAutoStart,
            wasRunning: state.value.isRunning
        })

        const wasRunning = state.value.isRunning

        if (state.value.isRunning && !shouldAutoStart) {
            console.log('⏸️ [TimerStore] Stopping timer due to manual switch')
            state.value.isRunning = false
            stopTimer()
        }

        // NEW: Reset the "was started" flag when switching types
        state.value.wasTimerStartedThisCycle = false

        state.value.timerType = type

        if (!shouldAutoStart || !wasRunning) {
            state.value.timeLeft = getCurrentCycleDuration()
            console.log('⏰ [TimerStore] Timer type switched, timeLeft set to:', state.value.timeLeft)
        } else {
            console.log('⏰ [TimerStore] Auto-transition while running, setting timeLeft to full duration')
            state.value.timeLeft = getCurrentCycleDuration()
        }

        if (shouldAutoStart && wasRunning) {
            const shouldStart = (type === 'pomodoro' && state.value.autoStartPomodoros) ||
                ((type === 'shortBreak' || type === 'longBreak') && state.value.autoStartBreaks)

            if (shouldStart) {
                // NEW: Mark as started if auto-starting (for all timer types)
                state.value.wasTimerStartedThisCycle = true
                state.value.isRunning = true
                startTimer()
                console.log('▶️ [TimerStore] Auto-starting timer, wasTimerStartedThisCycle set to true')
            } else {
                state.value.isRunning = false
                console.log('⏸️ [TimerStore] Not auto-starting timer')
            }
        }

        // Update Discord presence after timer type change
        updateDiscordPresence()
    }

    async function startTimer() {
        console.log('▶️ [TimerStore] startTimer called, current interval:', timerInterval)

        if (timerInterval !== null) {
            console.log('⚠️ [TimerStore] Timer already running, skipping startTimer')
            return
        }

        console.log('🚀 [TimerStore] Starting timer interval...')
        timerInterval = window.setInterval(async () => {
            // Reduce logging frequency to every 30 seconds
            if (state.value.timeLeft % 30 === 0) {
                console.log('⏱️ [TimerStore] Timer tick - timeLeft:', state.value.timeLeft, 'timerType:', state.value.timerType)
            }

            if (state.value.timeLeft > 0) {
                state.value.timeLeft--

                // If we're in a pomodoro, count the time as focus time
                if (state.value.timerType === 'pomodoro') {
                    state.value.totalFocusTime++
                }
            } else {
                console.log('⏰ [TimerStore] Timer completed! Handling completion...')
                // Timer finished
                await handleTimerComplete()
            }
        }, 1000)

        console.log('✅ [TimerStore] Timer interval started with ID:', timerInterval)
    }

    function stopTimer() {
        console.log('⏹️ [TimerStore] stopTimer called, current interval:', timerInterval)

        if (timerInterval !== null) {
            clearInterval(timerInterval)
            timerInterval = null
            console.log('✅ [TimerStore] Timer interval cleared')
        } else {
            console.log('⚠️ [TimerStore] No timer interval to clear')
        }
    }

    async function toggleTimer() {
        console.log('🔄 [TimerStore] toggleTimer called:', {
            currentlyRunning: state.value.isRunning,
            timerType: state.value.timerType,
            canStartSession: canStartSession.value
        })

        if (!state.value.isRunning && !canStartSession.value && !state.value.activeSession) {
            console.log('❌ [TimerStore] Cannot start NEW sessions during break periods without active session')
            return { success: false, reason: 'Cannot start sessions during break periods' }
        }

        if (!state.value.isRunning) {
            // Starting/resuming timer
            console.log('▶️ [TimerStore] Starting timer...')

            // NEW: Mark that timer was started for this cycle
            state.value.wasTimerStartedThisCycle = true

            if (state.value.timerType === 'pomodoro') {
                console.log('🎯 [TimerStore] Pomodoro type, checking for active session...')
                const session = await checkAndCreateAutoSession()
                if (!session) {
                    console.error('❌ [TimerStore] Failed to get/create session')
                    return { success: false, reason: 'Failed to create session' }
                }

                if (!state.value.currentCycleId) {
                    console.log('🔄 [TimerStore] No active cycle, starting new pomodoro cycle...')
                    try {
                        const cycle = await db.timer.startPomodoroInSession(session.id!, state.value.timerType)
                        state.value.currentCycleId = cycle.id
                        state.value.currentSessionId = session.id!
                        console.log('✅ [TimerStore] New cycle started:', cycle.id)
                    } catch (error) {
                        console.error('❌ [TimerStore] Failed to start pomodoro in session:', error)
                        return { success: false, reason: 'Failed to start cycle' }
                    }
                } else {
                    console.log('ℹ️ [TimerStore] Using existing cycle:', state.value.currentCycleId)
                }
            }

            state.value.isRunning = true
            startTimer()
        } else {
            // Pausing timer
            console.log('⏸️ [TimerStore] Pausing timer...')
            state.value.isRunning = false
            stopTimer()
        }

        // Update Discord presence after timer state change
        updateDiscordPresence()

        console.log('✅ [TimerStore] toggleTimer completed successfully')
        return { success: true }
    }

    async function skipTimer() {
        console.log('⏭️ [TimerStore] skipTimer called:', {
            timerType: state.value.timerType,
            wasRunning: state.value.isRunning,
            wasStarted: state.value.wasTimerStartedThisCycle, // NEW: Log this
            pomodoroCount: state.value.pomodoroCount,
            currentCycleId: state.value.currentCycleId
        })

        const wasRunning = state.value.isRunning

        if (state.value.timerType === 'pomodoro') {
            console.log('🎯 [TimerStore] Skipping pomodoro...')

            // NEW: Only complete and count if timer was actually started
            if (state.value.wasTimerStartedThisCycle) {
                console.log('✅ [TimerStore] Timer was started - completing cycle and counting')

                // Calculate time completed for this pomodoro
                const timeCompleted = state.value.pomodoroTime - state.value.timeLeft
                console.log('📊 [TimerStore] Time completed this pomodoro:', timeCompleted, 'seconds')

                // Complete the pomodoro cycle in the database
                if (state.value.currentCycleId && state.value.currentSessionId) {
                    try {
                        console.log('💾 [TimerStore] Completing cycle in database:', state.value.currentCycleId)
                        await db.timer.completePomodoroInSession(state.value.currentSessionId, state.value.currentCycleId)
                        console.log('✅ [TimerStore] Cycle completed in database')
                    } catch (error) {
                        console.error('❌ [TimerStore] Failed to complete pomodoro in session:', error)
                    }
                }

                // Update local state
                const oldTotalTime = state.value.totalFocusTime
                state.value.pomodoroCount++
                state.value.totalFocusTime += timeCompleted

                // Update session in database with new totals (don't update pomodoro count - DB handles this automatically)
                if (state.value.currentSessionId) {
                    try {
                        console.log('💾 [TimerStore] Updating session duration in database')
                        const updateData = {
                            duration: state.value.totalFocusTime
                        }
                        await db.timer.updateSession(state.value.currentSessionId, updateData)
                        console.log('✅ [TimerStore] Session updated in database with new totals')

                        // Refresh session data to get the correct pomodoro count from database
                        const refreshedSession = await db.timer.getActiveUserSession()
                        if (refreshedSession && refreshedSession.id === state.value.currentSessionId) {
                            state.value.pomodoroCount = refreshedSession.pomodoro_cycles_completed || 0
                            console.log('🔄 [TimerStore] Synced pomodoro count from database:', state.value.pomodoroCount)
                        }
                    } catch (error) {
                        console.error('❌ [TimerStore] Failed to update session in database:', error)
                    }
                }

            } else {
                console.log('⏭️ [TimerStore] Timer was NEVER started - skipping without counting')

                // NEW: Cancel the cycle instead of completing it
                if (state.value.currentCycleId && state.value.currentSessionId) {
                    try {
                        console.log('🗑️ [TimerStore] Canceling unstarted cycle:', state.value.currentCycleId)
                        await db.timer.cancelActiveCycle(state.value.currentSessionId, state.value.currentCycleId)
                        console.log('✅ [TimerStore] Unstarted cycle canceled')
                    } catch (error) {
                        console.error('❌ [TimerStore] Failed to cancel cycle:', error)
                    }
                }

                // DON'T increment pomodoro count
                // DON'T add focus time
                console.log('📊 [TimerStore] Pomodoro count unchanged:', state.value.pomodoroCount)
            }

            // Reset cycle ID after handling
            state.value.currentCycleId = null

            // Check if it's time for a long break (only if we actually completed pomodoros)
            if (state.value.pomodoroCount % state.value.longBreakInterval === 0 && state.value.pomodoroCount > 0) {
                console.log('🛌 [TimerStore] Time for long break!')
                switchTimerType('longBreak', wasRunning)
            } else {
                console.log('☕ [TimerStore] Time for short break!')
                switchTimerType('shortBreak', wasRunning)
            }
        } else {
            console.log('🎯 [TimerStore] Coming back from break, switching to pomodoro')
            switchTimerType('pomodoro', wasRunning)

            if (wasRunning && state.value.isRunning) {
                console.log('🔄 [TimerStore] Auto-starting, creating new cycle...')
                try {
                    const session = await checkAndCreateAutoSession()
                    if (session) {
                        const cycle = await db.timer.startPomodoroInSession(session.id!, 'pomodoro')
                        state.value.currentCycleId = cycle.id
                        state.value.currentSessionId = session.id!
                        // Mark as started since we auto-started this new cycle
                        state.value.wasTimerStartedThisCycle = true
                        console.log('✅ [TimerStore] New cycle created after break skip:', cycle.id, 'wasTimerStartedThisCycle set to true')
                        return
                    }
                } catch (error) {
                    console.error('❌ [TimerStore] Failed to start new pomodoro cycle after break skip:', error)
                }
            }
        }

        // NEW: Reset the "was started" flag for new cycle (only if we didn't auto-start)
        state.value.wasTimerStartedThisCycle = false

        console.log('✅ [TimerStore] skipTimer completed')
    }

    async function restartTimer() {
        console.log('🔄 [TimerStore] restartTimer called')

        // Stop the timer if it's running
        if (state.value.isRunning) {
            console.log('⏹️ [TimerStore] Stopping running timer')
            state.value.isRunning = false
            stopTimer()
        }

        // Reset to initial state
        const oldState = { ...state.value }
        state.value.timerType = 'pomodoro'
        state.value.timeLeft = state.value.pomodoroTime
        state.value.pomodoroCount = 0
        state.value.totalFocusTime = 0
        state.value.currentCycleId = null
        state.value.currentSessionId = null
        state.value.wasTimerStartedThisCycle = false

        console.log('🔄 [TimerStore] Timer state reset:', {
            from: oldState,
            to: { ...state.value }
        })
    }

    // ===== PRIVATE HELPERS =====
    async function handleTimerComplete() {
        console.log('🏁 [TimerStore] handleTimerComplete called for type:', state.value.timerType)

        // Handle pomodoro completion
        if (state.value.timerType === 'pomodoro') {
            console.log('🎯 [TimerStore] Completing pomodoro cycle in database...')

            // Complete the pomodoro cycle in the database
            if (state.value.currentCycleId && state.value.currentSessionId) {
                try {
                    await db.timer.completePomodoroInSession(state.value.currentSessionId, state.value.currentCycleId)
                    state.value.currentCycleId = null
                    console.log('✅ [TimerStore] Pomodoro cycle completed in database')
                } catch (error) {
                    console.error('❌ [TimerStore] Failed to complete pomodoro in session:', error)
                }
            }

            // Update pomodoro count and focus time
            const completedTime = state.value.pomodoroTime // Full pomodoro completed
            const oldTotalTime = state.value.totalFocusTime
            state.value.pomodoroCount++
            state.value.totalFocusTime += completedTime

            // FIX: Update session in database with new totals (don't update pomodoro count - DB handles this automatically)
            if (state.value.currentSessionId) {
                try {
                    console.log('💾 [TimerStore] Updating session duration after natural completion:', {
                        sessionId: state.value.currentSessionId,
                        completedTime,
                        oldTotal: oldTotalTime,
                        newTotal: state.value.totalFocusTime
                    })

                    const updateData = {
                        duration: state.value.totalFocusTime
                    }

                    await db.timer.updateSession(state.value.currentSessionId, updateData)
                    console.log('✅ [TimerStore] Session updated in database after natural completion')

                    // Refresh session data to get the correct pomodoro count from database
                    const refreshedSession = await db.timer.getActiveUserSession()
                    if (refreshedSession && refreshedSession.id === state.value.currentSessionId) {
                        state.value.pomodoroCount = refreshedSession.pomodoro_cycles_completed || 0
                        console.log('🔄 [TimerStore] Synced pomodoro count from database:', state.value.pomodoroCount)
                    }
                } catch (error) {
                    console.error('❌ [TimerStore] Failed to update session after natural completion:', error)
                }
            }

            console.log('📊 [TimerStore] Pomodoro count incremented to:', state.value.pomodoroCount)
        }

        // Auto-transition to the next timer state with auto-start check
        if (state.value.timerType === 'pomodoro') {
            // Check if it's time for a long break (only after completing actual pomodoros)
            if (state.value.pomodoroCount > 0 && state.value.pomodoroCount % state.value.longBreakInterval === 0) {
                console.log('🛌 [TimerStore] Transitioning to long break')
                switchTimerType('longBreak', true)
            } else {
                console.log('☕ [TimerStore] Transitioning to short break')
                switchTimerType('shortBreak', true)
            }
        } else {
            console.log('🎯 [TimerStore] Break complete, transitioning to pomodoro')
            // Coming back from a break, switch to pomodoro
            switchTimerType('pomodoro', true)

            // Always create a new pomodoro cycle when transitioning from break to pomodoro
            if (state.value.isRunning) {
                console.log('🔄 [TimerStore] Auto-starting, creating new cycle after break...')
                try {
                    const session = await checkAndCreateAutoSession()
                    if (session) {
                        const cycle = await db.timer.startPomodoroInSession(session.id!, 'pomodoro')
                        state.value.currentCycleId = cycle.id
                        state.value.currentSessionId = session.id!
                        console.log('✅ [TimerStore] New cycle created after break:', cycle.id)
                    }
                } catch (error) {
                    console.error('❌ [TimerStore] Failed to start new pomodoro cycle after break:', error)
                }
            }
        }
    }

    async function checkAndCreateAutoSession() {
        console.log('🔍 [TimerStore] checkAndCreateAutoSession called')

        // Prevent session creation during break periods
        if (!canStartSession.value) {
            console.log('❌ [TimerStore] Cannot create session during break periods')
            return null
        }

        // Check if there's an active user session
        console.log('🔍 [TimerStore] Checking for existing active session...')
        const existingSession = await db.timer.getActiveUserSession()

        if (!existingSession) {
            console.log('🆕 [TimerStore] No existing session, creating auto session...')

            // Generate auto session name
            const now = new Date()
            const timeOfDay = getTimeOfDayPrefix(now)
            const dateStr = now.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            })

            const autoSessionName = `${timeOfDay} - ${dateStr}`
            console.log('📝 [TimerStore] Auto session name:', autoSessionName)

            try {
                const autoSession = await db.timer.createUserSession(
                    autoSessionName,
                    'Auto-generated work session',
                    'General'
                )

                state.value.activeSession = autoSession
                console.log('✅ [TimerStore] Auto session created:', autoSession.id)
                return autoSession
            } catch (error) {
                console.error('❌ [TimerStore] Failed to create auto session:', error)
                return null
            }
        }

        console.log('✅ [TimerStore] Using existing session:', existingSession.id)
        state.value.activeSession = existingSession
        return existingSession
    }

    // ===== SESSION MANAGEMENT =====
    async function createSession(sessionName: string, focus?: string, category?: string) {
        console.log('🆕 [TimerStore] createSession called:', { sessionName, focus, category })

        try {
            const session = await db.timer.createUserSession(sessionName, focus, category)

            state.value.activeSession = session
            state.value.pomodoroCount = session.pomodoro_cycles_completed || 0
            state.value.totalFocusTime = session.duration || 0

            // Reset timer to work cycle
            state.value.timerType = 'pomodoro'
            state.value.timeLeft = state.value.pomodoroTime
            state.value.currentCycleId = null
            state.value.currentSessionId = null
            state.value.wasTimerStartedThisCycle = false

            console.log('✅ [TimerStore] Session created successfully:', session.id)

            // Update Discord presence with new session
            updateDiscordPresence()

            return session
        } catch (error) {
            console.error('❌ [TimerStore] Failed to create session:', error)
            throw error
        }
    }

    async function endSession(endDate?: Date) {
        console.log('🏁 [TimerStore] endSession called')

        // Stop timer and complete any running cycle
        if (state.value.isRunning) {
            console.log('⏹️ [TimerStore] Stopping running timer before ending session')
            state.value.isRunning = false
            stopTimer()
        }

        if (!state.value.activeSession?.id) {
            console.log('⚠️ [TimerStore] No active session to end')
            return null
        }

        const sessionId = state.value.activeSession.id
        console.log('🏁 [TimerStore] Ending session:', sessionId)

        try {
            const endedSession = await db.timer.endUserSession(sessionId, endDate)

            // Reset timer state
            const oldState = { ...state.value }
            state.value.activeSession = null
            state.value.currentCycleId = null
            state.value.currentSessionId = null
            state.value.pomodoroCount = 0
            state.value.totalFocusTime = 0
            state.value.timerType = 'pomodoro'
            state.value.timeLeft = state.value.pomodoroTime
            state.value.isRunning = false
            state.value.wasTimerStartedThisCycle = false

            console.log('✅ [TimerStore] Session ended and state reset:', {
                endedSession: endedSession.id,
                oldState,
                newState: { ...state.value }
            })

            // Update Discord presence after session end
            updateDiscordPresence()

            return endedSession
        } catch (error) {
            console.error('❌ [TimerStore] Failed to end session:', error)
            throw error
        }
    }

    // ===== SETTINGS MANAGEMENT =====
    async function loadActiveSession() {
        console.log('📥 [TimerStore] loadActiveSession called')

        try {
            const activeSession = await db.timer.getActiveUserSession()

            if (activeSession) {
                console.log('📥 [TimerStore] Active session found:', activeSession.id)
                state.value.activeSession = activeSession
                state.value.pomodoroCount = activeSession.pomodoro_cycles_completed || 0
                state.value.totalFocusTime = activeSession.duration || 0

                // Don't auto-resume timer, just load the session data
                state.value.timerType = 'pomodoro'
                state.value.timeLeft = state.value.pomodoroTime
                state.value.isRunning = false
                state.value.wasTimerStartedThisCycle = false

                console.log('✅ [TimerStore] Active session loaded:', {
                    id: activeSession.id,
                    pomodoroCount: state.value.pomodoroCount,
                    totalFocusTime: state.value.totalFocusTime
                })
            } else {
                console.log('ℹ️ [TimerStore] No active session found')
            }
        } catch (error) {
            console.error('❌ [TimerStore] Failed to load active session:', error)
        }
    }

    async function loadSettings() {
        console.log('⚙️ [TimerStore] loadSettings called')

        try {
            const settings = await db.timer.getSettings()

            const oldSettings = {
                pomodoroTime: state.value.pomodoroTime,
                shortBreakTime: state.value.shortBreakTime,
                longBreakTime: state.value.longBreakTime,
                longBreakInterval: state.value.longBreakInterval,
                autoStartBreaks: state.value.autoStartBreaks,
                autoStartPomodoros: state.value.autoStartPomodoros
            }

            state.value.pomodoroTime = settings.work_duration
            state.value.shortBreakTime = settings.short_break_duration
            state.value.longBreakTime = settings.long_break_duration
            state.value.longBreakInterval = settings.long_break_interval
            state.value.autoStartBreaks = settings.auto_start_breaks === 1
            state.value.autoStartPomodoros = settings.auto_start_work === 1

            // Only update timeLeft if timer is not running
            if (!state.value.isRunning) {
                state.value.timeLeft = getCurrentCycleDuration()
                console.log('⏰ [TimerStore] Settings updated, timeLeft adjusted to:', state.value.timeLeft)
            } else {
                console.log('⏰ [TimerStore] Settings updated, preserving current timeLeft:', state.value.timeLeft)
            }

            console.log('✅ [TimerStore] Settings loaded:', {
                from: oldSettings,
                to: {
                    pomodoroTime: state.value.pomodoroTime,
                    shortBreakTime: state.value.shortBreakTime,
                    longBreakTime: state.value.longBreakTime,
                    longBreakInterval: state.value.longBreakInterval,
                    autoStartBreaks: state.value.autoStartBreaks,
                    autoStartPomodoros: state.value.autoStartPomodoros
                }
            })
        } catch (error) {
            console.error('❌ [TimerStore] Failed to load timer settings:', error)
        }
    }

    async function updateSettings(newSettings: {
        pomodoroTime: number
        shortBreakTime: number
        longBreakTime: number
        longBreakInterval: number
        autoStartBreaks: boolean
        autoStartPomodoros: boolean
    }) {
        console.log('⚙️ [TimerStore] updateSettings called:', newSettings)

        try {
            await db.timer.updateSettings({
                work_duration: newSettings.pomodoroTime,
                short_break_duration: newSettings.shortBreakTime,
                long_break_duration: newSettings.longBreakTime,
                long_break_interval: newSettings.longBreakInterval,
                auto_start_breaks: newSettings.autoStartBreaks ? 1 : 0,
                auto_start_work: newSettings.autoStartPomodoros ? 1 : 0
            })

            // Update local values
            state.value.pomodoroTime = newSettings.pomodoroTime
            state.value.shortBreakTime = newSettings.shortBreakTime
            state.value.longBreakTime = newSettings.longBreakTime
            state.value.longBreakInterval = newSettings.longBreakInterval
            state.value.autoStartBreaks = newSettings.autoStartBreaks
            state.value.autoStartPomodoros = newSettings.autoStartPomodoros

            // Update current timer according to its type
            if (state.value.timerType === 'pomodoro') {
                state.value.timeLeft = state.value.pomodoroTime
            } else if (state.value.timerType === 'shortBreak') {
                state.value.timeLeft = state.value.shortBreakTime
            } else if (state.value.timerType === 'longBreak') {
                state.value.timeLeft = state.value.longBreakTime
            }

            // Reset timer if running
            if (state.value.isRunning) {
                console.log('⏹️ [TimerStore] Resetting timer due to settings change')
                state.value.isRunning = false
                stopTimer()
            }

            console.log('✅ [TimerStore] Settings updated successfully')
        } catch (error) {
            console.error('❌ [TimerStore] Failed to update timer settings:', error)
            throw error
        }
    }

    // ===== INITIALIZATION =====
    async function initialize() {
        console.log('🚀 [TimerStore] initialize called')

        try {
            await loadSettings()
            await loadActiveSession()
            console.log('✅ [TimerStore] Initialization completed successfully')
        } catch (error) {
            console.error('❌ [TimerStore] Initialization failed:', error)
        }
    }

    // ===== CLEANUP =====
    function cleanup() {
        console.log('🧹 [TimerStore] cleanup called')

        if (timerInterval) {
            clearInterval(timerInterval)
            timerInterval = null
            console.log('✅ [TimerStore] Timer interval cleaned up')
        } else {
            console.log('ℹ️ [TimerStore] No timer interval to clean up')
        }
    }

    // ===== PUBLIC API =====
    return {
        // State (readonly)
        state: computed(() => state.value),

        // Computed values
        formattedTime,
        formattedMinutes,
        formattedSeconds,
        formattedFocusTime,
        sessionActive,
        canStartSession,

        // Timer controls
        switchTimerType,
        toggleTimer,
        skipTimer,
        restartTimer,

        // Session management
        createSession,
        endSession,

        // Settings management
        loadSettings,
        updateSettings,

        // Data loading
        loadActiveSession,
        initialize,
        cleanup,

        // Direct state access (for compatibility with existing components)
        isRunning: computed(() => state.value.isRunning),
        timerType: computed(() => state.value.timerType),
        timeLeft: computed(() => state.value.timeLeft),
        activeSession: computed(() => state.value.activeSession),
        pomodoroCount: computed(() => state.value.pomodoroCount),
        totalFocusTime: computed(() => state.value.totalFocusTime),
        pomodoroTime: computed(() => state.value.pomodoroTime),
        shortBreakTime: computed(() => state.value.shortBreakTime),
        longBreakTime: computed(() => state.value.longBreakTime),
        longBreakInterval: computed(() => state.value.longBreakInterval),
        autoStartBreaks: computed(() => state.value.autoStartBreaks),
        autoStartPomodoros: computed(() => state.value.autoStartPomodoros),
        wasTimerStartedThisCycle: computed(() => state.value.wasTimerStartedThisCycle)
    }
})