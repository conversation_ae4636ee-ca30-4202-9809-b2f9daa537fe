gitstats is a statistics generator for git repositories.  It is mostly intended
for developers, as a way to check some development statistics for a project.

Currently it produces only HTML output with tables and graphs.

Requirements
============
- Python (>= 2.6.0)
- Git (>= 1.5.2.4)
- Gnuplot (>= 4.0.0)
- a git repository (bare clone will work as well)

The above versions are not absolute requirements; older versions may work also.

Recommended
===========
- Lots of memory and fast disk for large projects

Contributions
=============
Patches should be sent under "GPLv2 or later" license - this will allow
upgrading to newer GPL versions if they are sensible.
